# Printer Service App

A cross-platform Electron application that provides a socket.io server for web applications to send print jobs to local printers.

## Features

### Core Functionality
- 🖨️ **Local Printer Access**: Connect web applications to local printers
- 🌐 **Socket.IO Server**: Real-time communication via WebSocket
- 🖥️ **Cross-Platform**: Works on Windows, macOS, and Linux
- 📦 **Easy Installation**: Multiple package formats available
- 🔧 **System Tray**: Runs in background with system tray integration
- 🔒 **CORS Support**: Configurable allowed origins for security

### Dynamic System Features (NEW!)
- 🔥 **Smart Firewall Management**: Automatic firewall detection and configuration
- 🔐 **Dynamic Admin Permissions**: Intelligent admin rights detection and elevation
- 🌐 **Internet Connectivity Checks**: Automatic network status monitoring
- 🔌 **Dynamic Port Allocation**: Automatic port detection and configuration (3001-3100)
- 🚀 **Background Operation**: Seamless background running with system tray
- ⚡ **Auto-Start Support**: Optional system boot auto-start configuration
- 📊 **System Status Dashboard**: Real-time system status monitoring in UI

## Installation

### Linux
- **AppImage**: `./Printer Service-1.0.0.AppImage`
- **DEB Package**: `sudo dpkg -i printer-service-app_1.0.0_amd64.deb`
- **RPM Package**: `sudo rpm -i printer-service-app-1.0.0.x86_64.rpm`

### Windows
- Run the installer: `Printer Service Setup.exe`

### macOS
- Mount the DMG and drag to Applications folder

## Development

### Prerequisites
- Node.js 14+
- npm or yarn

### Setup
```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for current platform
npm run build

# Build for all platforms
npm run dist
```

### Testing
```bash
# Test socket connection (requires service to be running)
node test-connection.js
```

## Usage

### Starting the Service
The application runs a Socket.IO server on port 3001 by default.

### Web Application Integration
```javascript
// Connect to the printer service
const socket = io('http://localhost:3001');

// Get available printers
socket.emit('get-printers');
socket.on('printers-list', (printers) => {
  console.log('Available printers:', printers);
});

// Send a print job
socket.emit('print-job', {
  printerName: 'HP LaserJet Pro',
  content: 'Hello World!',
  type: 'TEXT',
  copies: 1,
  paperSize: 'A4'
});

// Handle print results
socket.on('print-success', (result) => {
  console.log('Print successful:', result.jobID);
});

socket.on('print-error', (error) => {
  console.error('Print failed:', error.message);
});
```

### Configuration
The service creates a configuration file at:
- **Windows**: `%APPDATA%/Printer Service/config.json`
- **macOS**: `~/Library/Application Support/Printer Service/config.json`
- **Linux**: `~/.config/Printer Service/config.json`

Default configuration:
```json
{
  "socketPort": 3001,
  "allowedOrigins": [
    "http://localhost:3000",
    "https://yourwebapp.com"
  ]
}
```

## API Reference

### Events

#### Client → Server
- `get-printers`: Request list of available printers
- `print-job`: Send a print job

#### Server → Client
- `printers-list`: List of available printers
- `printers-error`: Error getting printer list
- `print-success`: Print job completed successfully
- `print-error`: Print job failed

### Print Job Format
```javascript
{
  printerName: string,    // Name of the target printer
  content: string,        // Content to print
  type: 'TEXT' | 'PDF',  // Content type (default: 'TEXT')
  copies: number,         // Number of copies (default: 1)
  paperSize: string       // Paper size (default: 'A4')
}
```

## Security

- CORS is configured to only allow specific origins
- Firewall rules are automatically configured during installation
- The service only accepts connections from configured origins

## Troubleshooting

### Service Won't Start
- Check if port 3001 is available
- Verify firewall settings
- Check logs in the application

### Can't Connect from Web App
- Verify the web app origin is in `allowedOrigins`
- Check CORS configuration
- Ensure the service is running

### Print Jobs Fail
- Verify printer is online and accessible
- Check printer permissions
- Ensure correct printer name is used

## Building

### Requirements for Building
- **Windows builds**: Requires Windows or Wine on Linux
- **macOS builds**: Requires macOS
- **Linux builds**: Works on any platform

### Build Commands
```bash
# Build for specific platforms
npm run build:linux    # Linux (AppImage, DEB, RPM)
npm run build:win      # Windows (EXE, MSI) - Auto-installs Wine if needed
npm run build:mac      # macOS (DMG, PKG) - Requires macOS
npm run build:all      # All supported platforms
npm run dist           # Same as build:all

# Development builds
npm run build          # Current platform only
npm start              # Development mode
npm test              # Test socket connection
```

### Dynamic Features in Action
The application now includes intelligent system detection and configuration:

1. **Startup Sequence**:
   - Internet connectivity check
   - Firewall status detection
   - Admin rights verification
   - Dynamic port allocation (3001-3100)
   - Auto-start configuration

2. **Smart Permissions**:
   - Automatic admin elevation requests when needed
   - Platform-specific permission handling
   - Graceful degradation without admin rights

3. **Network Intelligence**:
   - Automatic port conflict resolution
   - Firewall rule creation with user consent
   - CORS origin validation

4. **Background Operation**:
   - System tray integration
   - Minimize to tray functionality
   - Auto-start on system boot (optional)

## License

MIT License - see LICENSE file for details.
