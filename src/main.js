const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ipc<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>u } = require('electron');
const path = require('path');
const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const log = require('electron-log');

// Keep a global reference of the window object
let mainWindow;
let tray = null;
let server;
let io;

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

// Socket server setup
function createSocketServer() {
  const expressApp = express();
  server = http.createServer(expressApp);
  
  // CORS configuration
  expressApp.use(cors({
    origin: ['http://localhost:3000', 'https://yourwebapp.com'],
    methods: ['GET', 'POST'],
    credentials: true
  }));

  // Socket.IO setup
  io = socketIo(server, {
    cors: {
      origin: ['http://localhost:3000', 'https://yourwebapp.com'],
      methods: ['GET', 'POST'],
      credentials: true
    }
  });

  // Socket connection handling
  io.on('connection', (socket) => {
    log.info('Client connected:', socket.id);
    
    // Handle print job requests
    socket.on('print-job', (data) => {
      log.info('Print job received:', data);
      handlePrintJob(data, socket);
    });
    
    // Handle printer list request
    socket.on('get-printers', () => {
      getPrinterList(socket);
    });
    
    socket.on('disconnect', () => {
      log.info('Client disconnected:', socket.id);
    });
  });

  // Start server
  const PORT = process.env.PORT || 3001;
  server.listen(PORT, () => {
    log.info(`Socket server running on port ${PORT}`);
  });
}

// Print job handler
function handlePrintJob(jobData, socket) {
  try {
    // Import printer library
    const printer = require('node-printer');
    
    // Validate job data
    if (!jobData.printerName || !jobData.content) {
      socket.emit('print-error', { message: 'Invalid print job data' });
      return;
    }
    
    // Print options
    const options = {
      printer: jobData.printerName,
      type: jobData.type || 'TEXT',
      options: {
        copies: jobData.copies || 1,
        paperSize: jobData.paperSize || 'A4'
      }
    };
    
    // Send print job
    printer.printDirect({
      data: jobData.content,
      printer: jobData.printerName,
      type: options.type,
      options: options.options,
      success: function(jobID) {
        log.info('Print job sent successfully:', jobID);
        socket.emit('print-success', { jobID, message: 'Print job sent successfully' });
      },
      error: function(err) {
        log.error('Print job error:', err);
        socket.emit('print-error', { message: err.message });
      }
    });
    
  } catch (error) {
    log.error('Print handler error:', error);
    socket.emit('print-error', { message: error.message });
  }
}

// Get printer list
function getPrinterList(socket) {
  try {
    const printer = require('node-printer');
    const printers = printer.getPrinters();
    socket.emit('printers-list', printers);
  } catch (error) {
    log.error('Get printers error:', error);
    socket.emit('printers-error', { message: error.message });
  }
}

// Create the main window
function createWindow() {
  const iconPath = path.join(__dirname, '../build/icon.png');
  const windowOptions = {
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    }
  };

  // Only add icon if it exists
  if (require('fs').existsSync(iconPath)) {
    windowOptions.icon = iconPath;
  }

  mainWindow = new BrowserWindow(windowOptions);

  // Load the app
  mainWindow.loadFile('src/renderer/index.html');

  // Open DevTools in development
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle minimize to tray
  mainWindow.on('minimize', (event) => {
    event.preventDefault();
    mainWindow.hide();
  });

  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
    }
  });
}

// Create system tray
function createTray() {
  try {
    const iconPath = path.join(__dirname, '../build/icon.png');

    // Check if icon exists, if not, skip tray creation for now
    if (!require('fs').existsSync(iconPath)) {
      console.log('Icon file not found, skipping tray creation');
      return;
    }

    tray = new Tray(iconPath);
  
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show App',
      click: () => {
        mainWindow.show();
      }
    },
    {
      label: 'Quit',
      click: () => {
        app.isQuiting = true;
        app.quit();
      }
    }
  ]);
  
  tray.setToolTip('Printer Service');
  tray.setContextMenu(contextMenu);
  
  tray.on('click', () => {
    mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show();
  });
  } catch (error) {
    console.error('Failed to create tray:', error.message);
    log.error('Tray creation failed:', error);
  }
}

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createTray();
  createSocketServer();
  
  // Auto-start on system boot (optional)
  if (!app.getLoginItemSettings().openAtLogin) {
    app.setLoginItemSettings({
      openAtLogin: true,
      path: process.execPath
    });
  }
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// IPC handlers
ipcMain.on('get-app-info', (event) => {
  event.reply('app-info', {
    name: app.getName(),
    version: app.getVersion(),
    socketPort: 3001
  });
});

// Handle app quit
app.on('before-quit', () => {
  if (server) {
    server.close();
  }
});