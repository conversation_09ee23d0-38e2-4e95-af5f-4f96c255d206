{"name": "statuses", "description": "HTTP status utility", "version": "2.0.1", "repository": "jshttp/statuses", "license": "MIT", "files": ["HISTORY.md", "index.js", "codes.json", "LICENSE"], "devDependencies": {"csv-parse": "4.14.2", "eslint": "7.17.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.22.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.1.0", "mocha": "8.2.1", "nyc": "15.1.0", "raw-body": "2.4.1", "stream-to-array": "2.3.0"}, "engines": {"node": ">= 0.8"}}