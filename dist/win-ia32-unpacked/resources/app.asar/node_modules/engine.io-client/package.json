{"name": "engine.io-client", "description": "Client for the realtime Engine", "license": "MIT", "version": "6.6.3", "main": "./build/cjs/index.js", "module": "./build/esm/index.js", "exports": {"./package.json": "./package.json", "./dist/engine.io.esm.min.js": "./dist/engine.io.esm.min.js", "./dist/engine.io.js": "./dist/engine.io.js", "./dist/engine.io.min.js": "./dist/engine.io.min.js", ".": {"import": {"types": "./build/esm/index.d.ts", "node": "./build/esm-debug/index.js", "default": "./build/esm/index.js"}, "require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}}, "./debug": {"import": {"types": "./build/esm/index.d.ts", "default": "./build/esm-debug/index.js"}, "require": {"types": "./build/cjs/index.d.ts", "default": "./build/cjs/index.js"}}}, "types": "build/esm/index.d.ts", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1", "xmlhttprequest-ssl": "~2.1.1"}, "browser": {"./test/node.js": false, "./build/esm/transports/polling-xhr.node.js": "./build/esm/transports/polling-xhr.js", "./build/esm/transports/websocket.node.js": "./build/esm/transports/websocket.js", "./build/esm/globals.node.js": "./build/esm/globals.js", "./build/cjs/transports/polling-xhr.node.js": "./build/cjs/transports/polling-xhr.js", "./build/cjs/transports/websocket.node.js": "./build/cjs/transports/websocket.js", "./build/cjs/globals.node.js": "./build/cjs/globals.js"}, "homepage": "https://github.com/socketio/socket.io/tree/main/packages/engine.io-client#readme", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io.git"}, "files": ["build/", "dist/"]}