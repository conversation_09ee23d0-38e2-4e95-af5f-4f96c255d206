{"name": "electron-log", "version": "5.4.1", "description": "Just a simple logging module for your Electron application", "main": "src/index.js", "browser": "src/renderer/index.js", "engines": {"node": ">= 14"}, "typings": "src/index.d.ts", "repository": "megahertz/electron-log", "files": ["src/*", "!**/__specs__", "!**/.*", "main.js", "main.d.ts", "node.js", "node.d.ts", "preload.js", "renderer.js", "renderer.d.ts"], "author": "<PERSON><PERSON>", "license": "MIT", "homepage": "https://github.com/megahertz/electron-log#readme", "devDependencies": {"@types/node": "^20.10.6", "electron": "*", "eslint": "^8.56.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "humile": "^0.5.3", "nw": "^0.83.0", "typescript": "^5.3.3", "vite": "^4.5.9", "vite-plugin-electron": "^0.15.5", "vite-plugin-electron-renderer": "^0.14.5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}}