{"name": "socket.io", "version": "4.8.1", "description": "node.js realtime framework server", "files": ["dist/", "client-dist/", "wrapper.mjs", "!**/*.tsbuildinfo"], "directories": {"doc": "docs/", "example": "example/", "lib": "lib/", "test": "test/"}, "type": "commonjs", "main": "./dist/index.js", "exports": {"types": "./dist/index.d.ts", "import": "./wrapper.mjs", "require": "./dist/index.js"}, "types": "./dist/index.d.ts", "license": "MIT", "homepage": "https://github.com/socketio/socket.io/tree/main/packages/socket.io#readme", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io.git"}, "dependencies": {"accepts": "~1.3.4", "base64id": "~2.0.0", "cors": "~2.8.5", "debug": "~4.3.2", "engine.io": "~6.6.0", "socket.io-adapter": "~2.5.2", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.2.0"}, "tsd": {"directory": "test"}}