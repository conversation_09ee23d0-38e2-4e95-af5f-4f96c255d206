var _isArrayLike = require('./_isArrayLike.js');
var values = require('./values.js');
var _getLength = require('./_getLength.js');
var random = require('./random.js');
var toArray = require('./toArray.js');

// Sample **n** random values from a collection using the modern version of the
// [<PERSON><PERSON> shuffle](https://en.wikipedia.org/wiki/<PERSON>–<PERSON>_shuffle).
// If **n** is not specified, returns a single random element.
// The internal `guard` argument allows it to work with `_.map`.
function sample(obj, n, guard) {
  if (n == null || guard) {
    if (!_isArrayLike(obj)) obj = values(obj);
    return obj[random(obj.length - 1)];
  }
  var sample = toArray(obj);
  var length = _getLength(sample);
  n = Math.max(Math.min(n, length), 0);
  var last = length - 1;
  for (var index = 0; index < n; index++) {
    var rand = random(index, last);
    var temp = sample[index];
    sample[index] = sample[rand];
    sample[rand] = temp;
  }
  return sample.slice(0, n);
}

module.exports = sample;
