{"name": "engine.io", "version": "6.6.4", "description": "The realtime engine behind Socket.IO. Provides the foundation of a bidirectional connection between client and server", "type": "commonjs", "main": "./build/engine.io.js", "types": "./build/engine.io.d.ts", "exports": {"types": "./build/engine.io.d.ts", "import": "./wrapper.mjs", "require": "./build/engine.io.js"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.7.2", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1"}, "homepage": "https://github.com/socketio/socket.io/tree/main/packages/engine.io#readme", "repository": {"type": "git", "url": "git+https://github.com/socketio/socket.io.git"}, "files": ["build/", "wrapper.mjs"], "engines": {"node": ">=10.2.0"}}