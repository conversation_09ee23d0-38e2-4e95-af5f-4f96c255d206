{"name": "es-object-atoms", "version": "1.1.1", "description": "ES Object-related atoms: Object, ToObject, RequireObjectCoercible", "main": "index.js", "exports": {".": "./index.js", "./RequireObjectCoercible": "./RequireObjectCoercible.js", "./isObject": "./isObject.js", "./ToObject": "./ToObject.js", "./package.json": "./package.json"}, "sideEffects": false, "repository": {"type": "git", "url": "git+https://github.com/ljharb/es-object-atoms.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/ljharb/es-object-atoms#readme", "dependencies": {"es-errors": "^1.3.0"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "^8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}