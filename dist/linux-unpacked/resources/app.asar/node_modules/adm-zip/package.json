{"name": "adm-zip", "version": "0.4.16", "description": "Javascript implementation of zip for nodejs with support for electron original-fs. Allows user to create or extract zip files both in memory or to/from disk", "homepage": "https://github.com/cthackers/adm-zip", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/cthackers)", "license": "MIT", "files": ["adm-zip.js", "headers", "methods", "util", "zipEntry.js", "zipFile.js", "MIT-LICENSE.txt"], "main": "adm-zip.js", "repository": {"type": "git", "url": "https://github.com/cthackers/adm-zip.git"}, "engines": {"node": ">=0.3.0"}, "devDependencies": {"chai": "^4.1.2", "mocha": "^8.0.1", "rimraf": "^2.6.2"}}