directories:
  output: dist
  buildResources: build
appId: com.yourcompany.printerservice
productName: Printer Service
files:
  - filter:
      - src/**/*
      - node_modules/**/*
      - package.json
      - README.md
      - LICENSE
extraResources:
  - from: build
    to: build
    filter:
      - '*.png'
      - '*.ico'
      - '*.icns'
afterPack: ./install.js
protocols:
  - name: Printer Service Protocol
    schemes:
      - printerservice
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: build/icon.ico
  publisherName: Your Company
  requestedExecutionLevel: requireAdministrator
mac:
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  icon: build/icon.icns
  category: public.app-category.utilities
  hardenedRuntime: true
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  gatekeeperAssess: false
linux:
  target:
    - AppImage
    - deb
    - rpm
  icon: build/icon.png
  category: Utility
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  runAfterFinish: true
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeader: build/installerHeader.bmp
  installerHeaderIcon: build/icon.ico
  include: build/installer.nsh
dmg:
  background: build/background.png
  iconSize: 100
  contents:
    - x: 110
      'y': 150
    - x: 240
      'y': 150
      type: link
      path: /Applications
deb:
  depends:
    - libnotify4
    - libnss3
    - libxss1
    - libgconf-2-4
rpm:
  depends:
    - libnotify
    - nss
    - libXScrnSaver
electronVersion: 27.3.11
