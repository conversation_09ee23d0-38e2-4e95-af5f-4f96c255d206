const io = require('socket.io-client');

// Test the socket connection
function testConnection() {
  console.log('Testing socket connection to printer service...');
  
  const socket = io('http://localhost:3001');
  
  socket.on('connect', () => {
    console.log('✅ Connected to printer service!');
    console.log('Socket ID:', socket.id);
    
    // Test getting printer list
    console.log('📋 Requesting printer list...');
    socket.emit('get-printers');
    
    // Test a print job
    setTimeout(() => {
      console.log('🖨️ Sending test print job...');
      socket.emit('print-job', {
        printerName: 'Test Printer',
        content: 'Hello World - Test Print Job',
        type: 'TEXT',
        copies: 1
      });
    }, 1000);
    
    // Disconnect after 3 seconds
    setTimeout(() => {
      console.log('👋 Disconnecting...');
      socket.disconnect();
    }, 3000);
  });
  
  socket.on('printers-list', (printers) => {
    console.log('📋 Available printers:', printers.length);
    printers.forEach((printer, index) => {
      console.log(`  ${index + 1}. ${printer.name} (${printer.status})`);
    });
  });
  
  socket.on('printers-error', (error) => {
    console.log('❌ Error getting printers:', error.message);
  });
  
  socket.on('print-success', (result) => {
    console.log('✅ Print job successful:', result.message);
    console.log('Job ID:', result.jobID);
  });
  
  socket.on('print-error', (error) => {
    console.log('❌ Print job failed:', error.message);
  });
  
  socket.on('disconnect', () => {
    console.log('👋 Disconnected from printer service');
    process.exit(0);
  });
  
  socket.on('connect_error', (error) => {
    console.log('❌ Connection failed:', error.message);
    console.log('Make sure the printer service is running (npm start)');
    process.exit(1);
  });
}

if (require.main === module) {
  testConnection();
}

module.exports = testConnection;
