# 🪟 Windows Deployment Guide

## ✅ Successfully Built Windows Packages

### 📦 Available Packages

| Package | File | Size | Description |
|---------|------|------|-------------|
| **NSIS Installer** | `Printer Service-1.0.0.exe` | 138.77 MB | Full installer with wizard (x64 + ia32) |
| **Portable x64** | `Printer Service-1.0.0-x64.exe` | 71.97 MB | Standalone 64-bit executable |
| **Portable ia32** | `Printer Service-1.0.0-ia32.exe` | 67.12 MB | Standalone 32-bit executable |

## 🚀 How to Deploy on Windows

### Method 1: NSIS Installer (Recommended for End Users)

**File:** `Printer Service-1.0.0.exe`

1. **Download** the installer file
2. **Right-click** and select "Run as administrator"
3. **Follow** the installation wizard
4. **Features:**
   - Installs to Program Files
   - Creates Start Menu shortcuts
   - Configures auto-start (optional)
   - Sets up firewall rules
   - Includes uninstaller

```bash
# Silent installation (for automation)
"Printer Service-1.0.0.exe" /S
```

### Method 2: Portable Executable (No Installation)

**Files:** 
- `Printer Service-1.0.0-x64.exe` (for 64-bit Windows)
- `Printer Service-1.0.0-ia32.exe` (for 32-bit Windows)

1. **Download** the appropriate portable executable
2. **Run directly** - no installation required
3. **Features:**
   - No installation needed
   - Runs from any location
   - Perfect for USB deployment
   - Settings stored locally

### Method 3: Automated Installation Script

**File:** `install-windows.bat` (in dist folder)

1. **Place** the installer and script in the same folder
2. **Right-click** `install-windows.bat` and select "Run as administrator"
3. **Features:**
   - Automatic installer detection
   - Firewall configuration
   - Error handling
   - Status reporting

## 🔧 System Requirements

### Minimum Requirements
- **OS:** Windows 10 or later
- **Architecture:** x64 or x86 (32-bit)
- **RAM:** 512 MB
- **Disk Space:** 200 MB
- **Network:** Internet connection for initial setup

### Recommended
- **OS:** Windows 11
- **Architecture:** x64
- **RAM:** 1 GB
- **Disk Space:** 500 MB
- **Permissions:** Administrator rights for full functionality

## 🛡️ Security & Permissions

### Administrator Rights
The application requests administrator privileges for:
- **Firewall configuration** - Adding rules for port 3001
- **Auto-start setup** - Registry modifications
- **System service access** - Printer system integration

### Firewall Configuration
Automatically configures Windows Firewall to allow:
- **Incoming connections** on port 3001 (configurable)
- **Socket.IO communication** for web applications
- **CORS-protected origins** only

### Windows Defender
The application is:
- **Not code-signed** (for development builds)
- **May trigger SmartScreen** warnings
- **Safe to run** - contains no malicious code

## 📁 Installation Locations

### NSIS Installer
- **Program Files:** `C:\Program Files\Printer Service\`
- **Start Menu:** `Start Menu\Programs\Printer Service`
- **Uninstaller:** `Control Panel\Programs\Uninstall a program`
- **Auto-start:** `HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run`

### Portable Executable
- **Application:** Runs from download location
- **Settings:** Stored in same directory as executable
- **Logs:** Created in application directory

## 🔄 Auto-Start Configuration

### Automatic (via Installer)
The NSIS installer can configure auto-start during installation.

### Manual Configuration
1. **Press** `Win + R`
2. **Type** `shell:startup`
3. **Copy** shortcut to Printer Service
4. **Or** use the application's built-in toggle

### Registry Method
```cmd
reg add "HKEY_CURRENT_USER\Software\Microsoft\Windows\CurrentVersion\Run" /v "PrinterService" /t REG_SZ /d "C:\Program Files\Printer Service\Printer Service.exe" /f
```

## 🌐 Network Configuration

### Default Port
- **Socket Server:** Port 3001
- **Automatically detects** port conflicts
- **Uses alternative ports** (3002-3100) if needed

### Firewall Rules
Automatically creates rules for:
```cmd
netsh advfirewall firewall add rule name="Printer Service" dir=in action=allow protocol=TCP localport=3001
```

## 🖨️ Printer Integration

### Supported Printers
- **Local USB printers**
- **Network printers**
- **Shared printers**
- **PDF printers**

### Print Spooler
- Uses Windows Print Spooler service
- Requires Print Spooler to be running
- Supports all Windows-compatible printers

## 🐛 Troubleshooting

### Installation Issues
```cmd
# Check if installer is blocked
powershell -Command "Unblock-File 'Printer Service-1.0.0.exe'"

# Run with verbose logging
"Printer Service-1.0.0.exe" /S /D=C:\PrinterService
```

### Runtime Issues
```cmd
# Check if service is running
tasklist | findstr "Printer Service"

# Check port availability
netstat -an | findstr :3001

# Check firewall rules
netsh advfirewall firewall show rule name="Printer Service"
```

### Permission Issues
1. **Run as administrator**
2. **Check UAC settings**
3. **Verify antivirus exclusions**
4. **Check Windows Defender SmartScreen**

## 📊 Deployment Statistics

### Build Information
- **Electron Version:** 27.3.11
- **Node.js Version:** Built-in with Electron
- **Architecture Support:** x64, ia32
- **Build Platform:** Cross-compiled on Linux with Wine

### Package Sizes
- **NSIS Installer:** 138.77 MB (includes both architectures)
- **Portable x64:** 71.97 MB
- **Portable ia32:** 67.12 MB

## 🚀 Distribution Checklist

### Before Distribution
- [ ] Test on Windows 10 and 11
- [ ] Test both x64 and ia32 versions
- [ ] Verify firewall configuration works
- [ ] Test printer discovery and printing
- [ ] Check auto-start functionality
- [ ] Validate uninstallation process

### For Production
- [ ] Code sign the executables
- [ ] Create proper installer certificates
- [ ] Set up update mechanism
- [ ] Create user documentation
- [ ] Prepare support materials

## 📞 Support Information

### Log Locations
- **Application Logs:** `%APPDATA%\Printer Service\logs\`
- **Windows Event Log:** Application and System logs
- **Installer Logs:** `%TEMP%\` (for NSIS installer)

### Common Commands
```cmd
# Start service manually
"C:\Program Files\Printer Service\Printer Service.exe"

# Check service status
sc query "Printer Service"

# Restart Windows Print Spooler
net stop spooler && net start spooler
```

---

## 🎉 Ready for Windows Deployment!

Your Printer Service application is now ready for Windows deployment with:
- ✅ Multiple installation options
- ✅ Automatic system configuration
- ✅ Comprehensive error handling
- ✅ Professional installer experience
- ✅ Enterprise-ready features
